<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Energy.AI - Smart Energy Solutions</title>
    
    <!-- Critical CSS - Inline for immediate loading -->
    <style>
        :root {
            --primary-color: #1976d2;
            --secondary-color: #ff7200;
            --background-primary: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --glass-bg: rgba(255, 255, 255, 0.08);
            --glass-border: rgba(255, 255, 255, 0.15);
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--background-primary);
            background-attachment: fixed;
            color: var(--text-primary);
            overflow-x: hidden;
        }

        /* Loading Screen Styles */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--background-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.8s ease;
            overflow: hidden;
        }

        .loading-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .welcome-container {
            text-align: center;
            position: relative;
            z-index: 10;
            animation: welcomeFadeIn 1.5s ease-out;
        }

        .welcome-logo-section {
            margin-bottom: 3rem;
        }

        .welcome-logo {
            position: relative;
            display: inline-block;
            margin-bottom: 2rem;
        }

        .site-logo {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 60px;
            margin: 0 auto;
            animation: logoFloat 3s ease-in-out infinite;
            box-shadow: 0 20px 40px rgba(25, 118, 210, 0.3);
        }

        .site-logo::before {
            content: '⚡';
            color: white;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
        }

        .welcome-title {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 0.5rem;
            opacity: 0;
            animation: textSlideUp 1s ease-out 0.8s forwards;
        }

        .brand-name {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            opacity: 0;
            animation: textSlideUp 1s ease-out 1s forwards;
        }

        .welcome-subtitle {
            font-size: 1.2rem;
            color: var(--text-secondary);
            font-weight: 300;
            opacity: 0;
            animation: textSlideUp 1s ease-out 1.2s forwards;
            margin-bottom: 2rem;
        }

        .loading-progress {
            width: 300px;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            margin: 2rem auto 0;
            overflow: hidden;
            opacity: 0;
            animation: fadeIn 1s ease-out 1.5s forwards;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 2px;
            width: 0%;
            animation: progressLoad 3s ease-out 1.5s forwards;
        }

        .skip-loading-btn {
            position: absolute;
            bottom: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .skip-loading-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        /* Main Content */
        .main-content {
            display: none;
            min-height: 100vh;
            padding: 100px 20px;
            text-align: center;
        }

        .main-content.visible {
            display: block;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }

        .cta-button {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(25, 118, 210, 0.4);
        }

        /* Animations */
        @keyframes welcomeFadeIn {
            0% { opacity: 0; transform: scale(0.9); }
            100% { opacity: 1; transform: scale(1); }
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @keyframes textSlideUp {
            0% { opacity: 0; transform: translateY(30px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            0% { opacity: 0; }
            100% { opacity: 1; }
        }

        @keyframes progressLoad {
            0% { width: 0%; }
            100% { width: 100%; }
        }

        /* Floating shapes */
        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .shape {
            position: absolute;
            background: linear-gradient(135deg, rgba(255, 114, 0, 0.08), rgba(25, 118, 210, 0.08));
            border-radius: 50%;
            animation: float 25s infinite linear;
        }

        .shape:nth-child(1) { width: 120px; height: 120px; top: 15%; left: 8%; animation-delay: 0s; }
        .shape:nth-child(2) { width: 180px; height: 180px; top: 65%; left: 85%; animation-delay: 8s; }
        .shape:nth-child(3) { width: 90px; height: 90px; top: 85%; left: 15%; animation-delay: 16s; }
        .shape:nth-child(4) { width: 150px; height: 150px; top: 25%; left: 75%; animation-delay: 24s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
            25% { transform: translateY(-40px) rotate(90deg) scale(1.1); }
            50% { transform: translateY(-20px) rotate(180deg) scale(0.9); }
            75% { transform: translateY(-60px) rotate(270deg) scale(1.05); }
        }
    </style>
</head>
<body>
    <!-- Floating Shapes -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <!-- Welcome Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="welcome-container">
            <div class="welcome-logo-section">
                <div class="welcome-logo">
                    <div class="site-logo"></div>
                </div>
                <h1 class="welcome-title">Welcome to</h1>
                <h2 class="brand-name">Energy.AI</h2>
                <p class="welcome-subtitle">Smart Energy Solutions Powered by AI</p>
            </div>
            <div class="welcome-animation">
                <div class="loading-progress">
                    <div class="progress-bar"></div>
                </div>
                <button class="skip-loading-btn" onclick="skipLoading()">
                    Skip ⏭️
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <h1 class="hero-title">Energy.AI</h1>
        <p class="hero-subtitle">
            Transform your energy management with AI-powered solutions. 
            Optimize consumption, reduce costs, and embrace sustainable energy practices.
        </p>
        <button class="cta-button" onclick="alert('Feature coming soon!')">Get Started</button>
        <button class="cta-button" onclick="alert('Contact <NAME_EMAIL>')">Contact Us</button>
    </div>

    <script>
        // Simple and reliable loading script
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Energy.AI - DOM Content Loaded');

            let loadingHidden = false;

            function hideLoadingScreen() {
                if (loadingHidden) return;
                loadingHidden = true;

                console.log('Hiding loading screen...');
                const loadingScreen = document.getElementById('loadingScreen');
                const mainContent = document.getElementById('mainContent');
                
                if (loadingScreen) {
                    loadingScreen.classList.add('hidden');
                    
                    setTimeout(() => {
                        if (loadingScreen.parentNode) {
                            loadingScreen.remove();
                        }
                        if (mainContent) {
                            mainContent.classList.add('visible');
                        }
                        console.log('Loading screen removed, main content shown');
                    }, 800);
                }
            }

            // Auto-hide after 4 seconds
            setTimeout(hideLoadingScreen, 4000);

            // Also hide when window is fully loaded
            window.addEventListener('load', () => {
                setTimeout(hideLoadingScreen, 1000);
            });

            // Make function globally available
            window.hideLoadingScreen = hideLoadingScreen;
        });

        // Skip loading function
        function skipLoading() {
            console.log('Skip loading clicked');
            if (window.hideLoadingScreen) {
                window.hideLoadingScreen();
            }
        }

        // Error handling
        window.addEventListener('error', function(e) {
            console.error('JavaScript Error:', e.message, 'at', e.filename + ':' + e.lineno);
        });
    </script>
</body>
</html>
