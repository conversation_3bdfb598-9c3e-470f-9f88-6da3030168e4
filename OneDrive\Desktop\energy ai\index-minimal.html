<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Energy.AI - Smart Energy Solutions</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            color: white;
            overflow: hidden;
        }

        /* Loading Screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.8s ease;
        }

        .loading-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .welcome-container {
            text-align: center;
            animation: fadeIn 1.5s ease-out;
        }

        .logo {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #1976d2, #ff7200);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 60px;
            margin: 0 auto 30px;
            animation: pulse 2s infinite;
            box-shadow: 0 20px 40px rgba(25, 118, 210, 0.3);
        }

        .logo::before {
            content: '⚡';
            color: white;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
        }

        .welcome-title {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 10px;
            opacity: 0;
            animation: slideUp 1s ease-out 0.5s forwards;
        }

        .brand-name {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #1976d2, #ff7200);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            opacity: 0;
            animation: slideUp 1s ease-out 0.8s forwards;
        }

        .welcome-subtitle {
            font-size: 1.3rem;
            color: #ccc;
            margin-bottom: 40px;
            opacity: 0;
            animation: slideUp 1s ease-out 1.1s forwards;
        }

        .progress-container {
            width: 300px;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            margin: 0 auto 40px;
            overflow: hidden;
            opacity: 0;
            animation: fadeIn 1s ease-out 1.4s forwards;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, #1976d2, #ff7200);
            width: 0%;
            animation: loadProgress 3s ease-out 1.5s forwards;
        }

        .skip-btn {
            position: absolute;
            bottom: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .skip-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        /* Main Content */
        .main-content {
            display: none;
            min-height: 100vh;
            padding: 100px 20px;
            text-align: center;
            overflow-y: auto;
        }

        .main-content.visible {
            display: block;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #1976d2, #ff7200);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            color: #ccc;
            margin-bottom: 40px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }

        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .cta-btn {
            background: linear-gradient(135deg, #1976d2, #ff7200);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .cta-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(25, 118, 210, 0.4);
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes loadProgress {
            from { width: 0%; }
            to { width: 100%; }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero-title { font-size: 2.5rem; }
            .brand-name { font-size: 2.5rem; }
            .welcome-title { font-size: 1.8rem; }
            .logo { width: 80px; height: 80px; font-size: 40px; }
            .cta-buttons { flex-direction: column; align-items: center; }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="welcome-container">
            <div class="logo"></div>
            <h1 class="welcome-title">Welcome to</h1>
            <h2 class="brand-name">Energy.AI</h2>
            <p class="welcome-subtitle">Smart Energy Solutions Powered by AI</p>
            <div class="progress-container">
                <div class="progress-bar"></div>
            </div>
            <button class="skip-btn" onclick="skipLoading()">Skip ⏭️</button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <h1 class="hero-title">Energy.AI</h1>
        <p class="hero-subtitle">
            Transform your energy management with cutting-edge AI technology. 
            Optimize consumption, reduce costs, and embrace sustainable energy practices 
            for a smarter, greener future.
        </p>
        <div class="cta-buttons">
            <button class="cta-btn" onclick="showAlert('Get Started')">Get Started</button>
            <button class="cta-btn" onclick="showAlert('Learn More')">Learn More</button>
            <button class="cta-btn" onclick="showAlert('Contact Us')">Contact Us</button>
        </div>
    </div>

    <script>
        console.log('🚀 Energy.AI Minimal Version - Script Started');
        
        let isHidden = false;

        function hideLoadingScreen() {
            if (isHidden) {
                console.log('⚠️ Loading screen already hidden');
                return;
            }
            
            isHidden = true;
            console.log('🎯 Hiding loading screen...');
            
            const loadingScreen = document.getElementById('loadingScreen');
            const mainContent = document.getElementById('mainContent');
            
            if (loadingScreen) {
                console.log('✅ Loading screen element found');
                loadingScreen.classList.add('hidden');
                
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                    if (mainContent) {
                        mainContent.classList.add('visible');
                        console.log('✅ Main content shown');
                    }
                    console.log('✅ Loading screen hidden successfully');
                }, 800);
            } else {
                console.error('❌ Loading screen element not found');
            }
        }

        function skipLoading() {
            console.log('🔘 Skip button clicked');
            hideLoadingScreen();
        }

        function showAlert(action) {
            alert(`${action} feature will be available soon!\n\nThank you for your interest in Energy.AI`);
        }

        // Auto-hide after 4 seconds
        console.log('⏰ Setting auto-hide timer for 4 seconds');
        setTimeout(() => {
            console.log('⏰ Auto-hide timer triggered');
            hideLoadingScreen();
        }, 4000);

        // Backup: hide when page fully loads
        window.addEventListener('load', () => {
            console.log('📄 Window load event triggered');
            setTimeout(() => {
                console.log('📄 Backup timer triggered');
                hideLoadingScreen();
            }, 1000);
        });

        // Make functions globally available
        window.hideLoadingScreen = hideLoadingScreen;
        window.skipLoading = skipLoading;

        console.log('✅ Script setup complete');
    </script>
</body>
</html>
