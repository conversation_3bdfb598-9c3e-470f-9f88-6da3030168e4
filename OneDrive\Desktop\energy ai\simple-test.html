<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Loading Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            color: white;
            overflow: hidden;
        }

        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.8s ease;
        }

        .loading-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .welcome-container {
            text-align: center;
            animation: fadeIn 1s ease-out;
        }

        .logo {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #1976d2, #ff7200);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 50px;
            margin: 0 auto 20px;
            animation: pulse 2s infinite;
        }

        .logo::before {
            content: '⚡';
        }

        .title {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .brand {
            font-size: 3rem;
            font-weight: bold;
            background: linear-gradient(135deg, #1976d2, #ff7200);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #ccc;
            margin-bottom: 30px;
        }

        .progress {
            width: 200px;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            margin: 0 auto;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, #1976d2, #ff7200);
            width: 0%;
            animation: load 3s ease-out forwards;
        }

        .skip-btn {
            position: absolute;
            bottom: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .skip-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .main-content {
            display: none;
            padding: 100px 20px;
            text-align: center;
        }

        .main-content.visible {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes load {
            from { width: 0%; }
            to { width: 100%; }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="welcome-container">
            <div class="logo"></div>
            <h1 class="title">Welcome to</h1>
            <h2 class="brand">Energy.AI</h2>
            <p class="subtitle">Smart Energy Solutions Powered by AI</p>
            <div class="progress">
                <div class="progress-bar"></div>
            </div>
            <button class="skip-btn" onclick="hideLoading()">Skip ⏭️</button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <h1>Energy.AI Dashboard</h1>
        <p>Loading screen test completed successfully!</p>
        <p>The loading screen should have disappeared after 3 seconds.</p>
    </div>

    <script>
        console.log('Script started');
        
        let isHidden = false;

        function hideLoading() {
            if (isHidden) return;
            isHidden = true;
            
            console.log('Hiding loading screen...');
            const loadingScreen = document.getElementById('loadingScreen');
            const mainContent = document.getElementById('mainContent');
            
            if (loadingScreen) {
                loadingScreen.classList.add('hidden');
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                    if (mainContent) {
                        mainContent.classList.add('visible');
                    }
                    console.log('Loading screen hidden, main content shown');
                }, 800);
            }
        }

        // Auto hide after 3 seconds
        setTimeout(hideLoading, 3000);

        // Also hide when page is fully loaded
        window.addEventListener('load', () => {
            setTimeout(hideLoading, 1000);
        });

        console.log('Script setup complete');
    </script>
</body>
</html>
