<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loading Screen Diagnosis</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a2e;
            color: #00ff00;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #00ff00;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .section h2 {
            color: #ffff00;
            margin-top: 0;
        }
        .log {
            background: #000;
            border: 1px solid #333;
            border-radius: 4px;
            padding: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-entry.error { color: #ff4444; }
        .log-entry.warning { color: #ffaa00; }
        .log-entry.success { color: #44ff44; }
        .log-entry.info { color: #4488ff; }
        button {
            background: #00ff00;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }
        button:hover {
            background: #44ff44;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.good { background: rgba(0, 255, 0, 0.2); border: 1px solid #00ff00; }
        .status.bad { background: rgba(255, 0, 0, 0.2); border: 1px solid #ff0000; }
        .status.warning { background: rgba(255, 255, 0, 0.2); border: 1px solid #ffff00; }
        .test-result {
            display: inline-block;
            padding: 5px 10px;
            margin: 2px;
            border-radius: 4px;
            font-size: 12px;
        }
        .test-result.pass { background: #004400; color: #00ff00; }
        .test-result.fail { background: #440000; color: #ff4444; }
        .iframe-container {
            border: 2px solid #00ff00;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }
        iframe {
            width: 100%;
            height: 400px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Energy.AI Loading Screen Diagnosis Tool</h1>
        
        <div class="section">
            <h2>📊 System Status</h2>
            <div id="systemStatus" class="status">Initializing diagnosis...</div>
        </div>

        <div class="section">
            <h2>🧪 Quick Tests</h2>
            <button onclick="testMainFile()">Test Main Index.html</button>
            <button onclick="testCSS()">Test CSS Files</button>
            <button onclick="testJavaScript()">Test JavaScript</button>
            <button onclick="runAllTests()">Run All Tests</button>
            <button onclick="clearLog()">Clear Log</button>
            <div id="testResults" style="margin-top: 10px;"></div>
        </div>

        <div class="section">
            <h2>📝 Diagnostic Log</h2>
            <div id="diagnosticLog" class="log"></div>
        </div>

        <div class="section">
            <h2>🖼️ Live Preview</h2>
            <p>This iframe shows your actual index.html file:</p>
            <div class="iframe-container">
                <iframe src="index.html" id="previewFrame"></iframe>
            </div>
            <button onclick="reloadPreview()">Reload Preview</button>
            <button onclick="openInNewTab()">Open in New Tab</button>
        </div>

        <div class="section">
            <h2>🔧 Quick Fixes</h2>
            <button onclick="suggestFixes()">Suggest Fixes</button>
            <button onclick="createMinimalVersion()">Create Minimal Version</button>
            <div id="suggestions"></div>
        </div>
    </div>

    <script>
        let logEntries = [];

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            logEntries.push({ timestamp, message, type });
            updateLogDisplay();
        }

        function updateLogDisplay() {
            const logDiv = document.getElementById('diagnosticLog');
            logDiv.innerHTML = logEntries.map(entry => 
                `<div class="log-entry ${entry.type}">[${entry.timestamp}] ${entry.message}</div>`
            ).join('');
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            logEntries = [];
            updateLogDisplay();
        }

        function updateStatus(message, type = 'good') {
            const statusDiv = document.getElementById('systemStatus');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            addLog(`Status: ${message}`, type === 'good' ? 'success' : type === 'bad' ? 'error' : 'warning');
        }

        function testMainFile() {
            addLog('Testing main index.html file...', 'info');
            
            fetch('index.html')
                .then(response => {
                    if (response.ok) {
                        addLog('✅ Main index.html file is accessible', 'success');
                        return response.text();
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .then(html => {
                    const tests = [
                        { name: 'Loading Screen HTML', pattern: /id="loadingScreen"/, required: true },
                        { name: 'Skip Loading Function', pattern: /function skipLoading/, required: true },
                        { name: 'Hide Loading Function', pattern: /function hideLoadingScreen/, required: true },
                        { name: 'Welcome Container', pattern: /class="welcome-container"/, required: true },
                        { name: 'Progress Bar', pattern: /class="progress-bar"/, required: false },
                        { name: 'CSS Link', pattern: /welcome-screen\.css/, required: false }
                    ];

                    let passed = 0;
                    let failed = 0;

                    tests.forEach(test => {
                        const found = test.pattern.test(html);
                        if (found) {
                            addLog(`✅ ${test.name}: FOUND`, 'success');
                            passed++;
                        } else {
                            addLog(`❌ ${test.name}: MISSING${test.required ? ' (REQUIRED)' : ''}`, test.required ? 'error' : 'warning');
                            if (test.required) failed++;
                        }
                    });

                    updateTestResults('Main File', passed, failed);
                })
                .catch(error => {
                    addLog(`❌ Failed to load index.html: ${error.message}`, 'error');
                    updateTestResults('Main File', 0, 1);
                });
        }

        function testCSS() {
            addLog('Testing CSS files...', 'info');
            
            const cssFiles = [
                'css/welcome-screen.css',
                'css/glassmorphism.css',
                'css/styles.css'
            ];

            let promises = cssFiles.map(file => 
                fetch(file)
                    .then(response => ({ file, status: response.ok ? 'success' : 'error', response }))
                    .catch(error => ({ file, status: 'error', error }))
            );

            Promise.all(promises).then(results => {
                let passed = 0;
                let failed = 0;

                results.forEach(result => {
                    if (result.status === 'success') {
                        addLog(`✅ ${result.file}: LOADED`, 'success');
                        passed++;
                    } else {
                        addLog(`❌ ${result.file}: FAILED TO LOAD`, 'error');
                        failed++;
                    }
                });

                updateTestResults('CSS Files', passed, failed);
            });
        }

        function testJavaScript() {
            addLog('Testing JavaScript functions...', 'info');
            
            const iframe = document.getElementById('previewFrame');
            let passed = 0;
            let failed = 0;

            try {
                // Test if functions exist in the iframe
                setTimeout(() => {
                    try {
                        const iframeWindow = iframe.contentWindow;
                        const functions = ['skipLoading', 'hideLoadingScreen'];
                        
                        functions.forEach(funcName => {
                            if (typeof iframeWindow[funcName] === 'function') {
                                addLog(`✅ Function ${funcName}: AVAILABLE`, 'success');
                                passed++;
                            } else {
                                addLog(`❌ Function ${funcName}: NOT FOUND`, 'error');
                                failed++;
                            }
                        });

                        // Test if loading screen element exists
                        const loadingScreen = iframeWindow.document.getElementById('loadingScreen');
                        if (loadingScreen) {
                            addLog(`✅ Loading screen element: FOUND`, 'success');
                            addLog(`ℹ️ Loading screen display: ${getComputedStyle(loadingScreen).display}`, 'info');
                            addLog(`ℹ️ Loading screen opacity: ${getComputedStyle(loadingScreen).opacity}`, 'info');
                            passed++;
                        } else {
                            addLog(`❌ Loading screen element: NOT FOUND`, 'error');
                            failed++;
                        }

                        updateTestResults('JavaScript', passed, failed);
                    } catch (error) {
                        addLog(`❌ JavaScript test error: ${error.message}`, 'error');
                        updateTestResults('JavaScript', 0, 1);
                    }
                }, 2000);
            } catch (error) {
                addLog(`❌ JavaScript test setup error: ${error.message}`, 'error');
                updateTestResults('JavaScript', 0, 1);
            }
        }

        function updateTestResults(testName, passed, failed) {
            const resultsDiv = document.getElementById('testResults');
            const resultClass = failed === 0 ? 'pass' : 'fail';
            const resultText = `${testName}: ${passed} passed, ${failed} failed`;
            
            const existingResult = resultsDiv.querySelector(`[data-test="${testName}"]`);
            if (existingResult) {
                existingResult.remove();
            }

            const resultElement = document.createElement('div');
            resultElement.className = `test-result ${resultClass}`;
            resultElement.setAttribute('data-test', testName);
            resultElement.textContent = resultText;
            resultsDiv.appendChild(resultElement);
        }

        function runAllTests() {
            addLog('🚀 Running comprehensive diagnosis...', 'info');
            clearLog();
            setTimeout(() => addLog('🚀 Running comprehensive diagnosis...', 'info'), 100);
            
            testMainFile();
            setTimeout(testCSS, 1000);
            setTimeout(testJavaScript, 2000);
            
            setTimeout(() => {
                addLog('✅ All tests completed', 'success');
                updateStatus('Diagnosis completed - check results above', 'good');
            }, 4000);
        }

        function reloadPreview() {
            const iframe = document.getElementById('previewFrame');
            iframe.src = iframe.src;
            addLog('🔄 Preview reloaded', 'info');
        }

        function openInNewTab() {
            window.open('index.html', '_blank');
            addLog('🔗 Opened in new tab', 'info');
        }

        function suggestFixes() {
            const suggestionsDiv = document.getElementById('suggestions');
            suggestionsDiv.innerHTML = `
                <h3>💡 Suggested Fixes:</h3>
                <ul>
                    <li>✅ Check if all CSS files are loading properly</li>
                    <li>✅ Verify JavaScript functions are defined before use</li>
                    <li>✅ Ensure loading screen HTML structure is correct</li>
                    <li>✅ Test with browser developer tools console</li>
                    <li>✅ Try the simple-test.html file for comparison</li>
                </ul>
            `;
            addLog('💡 Suggestions displayed', 'info');
        }

        function createMinimalVersion() {
            addLog('🔧 Creating minimal version...', 'info');
            // This would create a minimal version - for now just log
            addLog('ℹ️ Use simple-test.html as a minimal working version', 'info');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('Diagnosis tool ready', 'good');
            addLog('🔍 Diagnosis tool initialized', 'success');
            
            // Auto-run basic tests after a delay
            setTimeout(runAllTests, 1000);
        });

        // Monitor iframe loading
        document.getElementById('previewFrame').addEventListener('load', function() {
            addLog('🖼️ Preview iframe loaded', 'success');
        });

        document.getElementById('previewFrame').addEventListener('error', function() {
            addLog('❌ Preview iframe failed to load', 'error');
        });
    </script>
</body>
</html>
