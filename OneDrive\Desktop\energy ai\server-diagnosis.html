<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Diagnosis - Energy.AI</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: #00ff00;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            border: 1px solid #00ff00;
        }
        .section {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #333;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .section h2 {
            color: #ffff00;
            margin-top: 0;
            border-bottom: 1px solid #333;
            padding-bottom: 10px;
        }
        .status-good { color: #00ff00; }
        .status-bad { color: #ff4444; }
        .status-warning { color: #ffaa00; }
        .info-box {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid #00ff00;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error-box {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid #ff4444;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .warning-box {
            background: rgba(255, 255, 0, 0.1);
            border: 1px solid #ffaa00;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background: #00ff00;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
            font-family: inherit;
        }
        button:hover {
            background: #44ff44;
        }
        .log {
            background: #000;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #333;
        }
        .test-result {
            font-weight: bold;
            margin-top: 10px;
        }
        .url-test {
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #333;
        }
        .url-test a {
            color: #00ff00;
            text-decoration: none;
        }
        .url-test a:hover {
            color: #44ff44;
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Energy.AI Server Diagnosis</h1>
            <p>Comprehensive server and loading screen diagnosis tool</p>
            <div id="currentStatus" class="status-good">Initializing...</div>
        </div>

        <div class="section">
            <h2>🌐 Server Connection Tests</h2>
            <div class="test-grid">
                <div class="test-item">
                    <h3>Current URL</h3>
                    <div id="currentUrl">Checking...</div>
                    <div id="urlResult" class="test-result"></div>
                </div>
                <div class="test-item">
                    <h3>Server Response</h3>
                    <div id="serverResponse">Testing...</div>
                    <div id="serverResult" class="test-result"></div>
                </div>
                <div class="test-item">
                    <h3>File Access</h3>
                    <div id="fileAccess">Checking files...</div>
                    <div id="fileResult" class="test-result"></div>
                </div>
                <div class="test-item">
                    <h3>Browser Info</h3>
                    <div id="browserInfo">Loading...</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📁 File Tests</h2>
            <div id="fileTests">
                <p>Testing file accessibility...</p>
            </div>
            <button onclick="testAllFiles()">Test All Files</button>
        </div>

        <div class="section">
            <h2>🔗 Quick Access Links</h2>
            <div class="url-test">
                <h3>Test Files:</h3>
                <p><a href="test-server.html" target="_blank">🧪 Server Test</a> - Basic server functionality</p>
                <p><a href="simple-test.html" target="_blank">⚡ Simple Loading Test</a> - Minimal loading screen</p>
                <p><a href="index-minimal.html" target="_blank">🎯 Minimal Index</a> - Simplified main page</p>
                <p><a href="index.html" target="_blank">🏠 Main Index</a> - Original main page</p>
                <p><a href="diagnose.html" target="_blank">🔍 Advanced Diagnosis</a> - Detailed analysis</p>
            </div>
        </div>

        <div class="section">
            <h2>🛠️ Troubleshooting Steps</h2>
            <div class="info-box">
                <h3>If the server is not responding:</h3>
                <ol>
                    <li>Check if Live Server extension is running in VS Code</li>
                    <li>Right-click on any HTML file → "Open with Live Server"</li>
                    <li>Check if port 5501 is available</li>
                    <li>Try a different port (5502, 5503, etc.)</li>
                    <li>Restart VS Code and try again</li>
                </ol>
            </div>
            
            <div class="warning-box">
                <h3>If files are not loading:</h3>
                <ol>
                    <li>Check file permissions</li>
                    <li>Ensure files are in the correct directory</li>
                    <li>Clear browser cache (Ctrl+F5)</li>
                    <li>Check browser console for errors (F12)</li>
                </ol>
            </div>
        </div>

        <div class="section">
            <h2>📊 Diagnostic Log</h2>
            <button onclick="clearLog()">Clear Log</button>
            <button onclick="runFullDiagnosis()">Run Full Diagnosis</button>
            <div id="diagnosticLog" class="log"></div>
        </div>
    </div>

    <script>
        let logEntries = [];

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#00ff00',
                error: '#ff4444',
                warning: '#ffaa00',
                success: '#44ff44'
            };
            
            logEntries.push({ timestamp, message, type });
            updateLogDisplay();
        }

        function updateLogDisplay() {
            const logDiv = document.getElementById('diagnosticLog');
            logDiv.innerHTML = logEntries.map(entry => 
                `<span style="color: ${entry.type === 'error' ? '#ff4444' : entry.type === 'warning' ? '#ffaa00' : '#00ff00'}">[${entry.timestamp}] ${entry.message}</span>`
            ).join('\n');
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            logEntries = [];
            updateLogDisplay();
        }

        function updateStatus(message, type = 'good') {
            const statusDiv = document.getElementById('currentStatus');
            statusDiv.className = `status-${type}`;
            statusDiv.textContent = message;
            addLog(`Status: ${message}`, type === 'good' ? 'success' : type);
        }

        function testCurrentUrl() {
            const currentUrl = window.location.href;
            document.getElementById('currentUrl').textContent = currentUrl;
            
            if (currentUrl.includes('127.0.0.1') || currentUrl.includes('localhost')) {
                document.getElementById('urlResult').innerHTML = '<span class="status-good">✅ Local server detected</span>';
                addLog('Local server URL detected', 'success');
            } else if (currentUrl.startsWith('file://')) {
                document.getElementById('urlResult').innerHTML = '<span class="status-warning">⚠️ File protocol - may have limitations</span>';
                addLog('File protocol detected - some features may not work', 'warning');
            } else {
                document.getElementById('urlResult').innerHTML = '<span class="status-good">✅ Web server detected</span>';
                addLog('Web server detected', 'success');
            }
        }

        function testServerResponse() {
            fetch(window.location.href)
                .then(response => {
                    if (response.ok) {
                        document.getElementById('serverResponse').textContent = `HTTP ${response.status} - OK`;
                        document.getElementById('serverResult').innerHTML = '<span class="status-good">✅ Server responding</span>';
                        addLog('Server responding correctly', 'success');
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .catch(error => {
                    document.getElementById('serverResponse').textContent = `Error: ${error.message}`;
                    document.getElementById('serverResult').innerHTML = '<span class="status-bad">❌ Server error</span>';
                    addLog(`Server error: ${error.message}`, 'error');
                });
        }

        function testFileAccess() {
            const testFiles = ['index.html', 'simple-test.html', 'test-server.html'];
            let successCount = 0;
            let totalFiles = testFiles.length;

            testFiles.forEach(file => {
                fetch(file)
                    .then(response => {
                        if (response.ok) {
                            successCount++;
                            addLog(`✅ ${file} - accessible`, 'success');
                        } else {
                            addLog(`❌ ${file} - HTTP ${response.status}`, 'error');
                        }
                        
                        if (successCount + (totalFiles - successCount) === totalFiles) {
                            const percentage = Math.round((successCount / totalFiles) * 100);
                            document.getElementById('fileAccess').textContent = `${successCount}/${totalFiles} files accessible (${percentage}%)`;
                            
                            if (percentage === 100) {
                                document.getElementById('fileResult').innerHTML = '<span class="status-good">✅ All files accessible</span>';
                            } else if (percentage > 50) {
                                document.getElementById('fileResult').innerHTML = '<span class="status-warning">⚠️ Some files inaccessible</span>';
                            } else {
                                document.getElementById('fileResult').innerHTML = '<span class="status-bad">❌ Most files inaccessible</span>';
                            }
                        }
                    })
                    .catch(error => {
                        addLog(`❌ ${file} - ${error.message}`, 'error');
                    });
            });
        }

        function updateBrowserInfo() {
            const info = {
                'User Agent': navigator.userAgent.substring(0, 50) + '...',
                'Platform': navigator.platform,
                'Language': navigator.language,
                'Online': navigator.onLine ? 'Yes' : 'No',
                'Cookies': navigator.cookieEnabled ? 'Enabled' : 'Disabled'
            };

            document.getElementById('browserInfo').innerHTML = Object.entries(info)
                .map(([key, value]) => `<strong>${key}:</strong> ${value}`)
                .join('<br>');
        }

        function testAllFiles() {
            const files = [
                'index.html',
                'simple-test.html', 
                'test-server.html',
                'index-minimal.html',
                'diagnose.html',
                'css/welcome-screen.css',
                'css/glassmorphism.css'
            ];

            const fileTestsDiv = document.getElementById('fileTests');
            fileTestsDiv.innerHTML = '<h3>Testing files...</h3>';

            files.forEach(file => {
                fetch(file)
                    .then(response => {
                        const status = response.ok ? '✅' : '❌';
                        const statusText = response.ok ? 'OK' : `Error ${response.status}`;
                        fileTestsDiv.innerHTML += `<p>${status} ${file} - ${statusText}</p>`;
                        addLog(`${file}: ${statusText}`, response.ok ? 'success' : 'error');
                    })
                    .catch(error => {
                        fileTestsDiv.innerHTML += `<p>❌ ${file} - ${error.message}</p>`;
                        addLog(`${file}: ${error.message}`, 'error');
                    });
            });
        }

        function runFullDiagnosis() {
            addLog('🚀 Starting full diagnosis...', 'info');
            clearLog();
            setTimeout(() => addLog('🚀 Starting full diagnosis...', 'info'), 100);
            
            testCurrentUrl();
            testServerResponse();
            testFileAccess();
            testAllFiles();
            
            updateStatus('Running full diagnosis...', 'warning');
            
            setTimeout(() => {
                updateStatus('Diagnosis completed - check results above', 'good');
                addLog('✅ Full diagnosis completed', 'success');
            }, 3000);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🔍 Server diagnosis tool initialized', 'success');
            updateBrowserInfo();
            
            // Auto-run basic tests
            setTimeout(() => {
                testCurrentUrl();
                testServerResponse();
                testFileAccess();
                updateStatus('Basic tests completed', 'good');
            }, 1000);
        });
    </script>
</body>
</html>
